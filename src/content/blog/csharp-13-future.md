---
title: "The Future is Delightful: A Peek into What's Next for C# 13"
description: "Exploring upcoming features and design philosophy for C# 13 and beyond"
pubDate: "Jun 22 2024"
heroImage: "https://ik.imagekit.io/quadrate/blogs/csharp-13.webp?updatedAt=1732702078737"
category: "Programming"
tags: ["C#", "programming", "language-design", "future-features"]
author: "<PERSON><PERSON>F.M <PERSON>rin"
authorImage: "https://ik.imagekit.io/quadrate/blogs/avatar.png?updatedAt=1732702078949"
---

# The Future is Delightful: A Peek into What's Next for C# 13

C# Lead Designer, <PERSON><PERSON>, recently gave a talk in Sydney, offering a glimpse into the exciting possibilities for the future of the language. While he couldn't reveal everything in the works for C# 13 and beyond, he shared key insights into the design philosophy and upcoming features that aim to make coding in C# even more enjoyable and efficient.

## Core Design Philosophy

Torgersen emphasized the C# team's core principle: the language should be a joy to use. They strive to eliminate frustrations and 'gotchas' that can make coding tedious, ensuring that C# remains a compelling choice for developers' next projects.

## Collection Expressions and Beyond

Collection Expressions were a standout feature in C# 12, unifying the syntax for creating different collection types. Future enhancements aim to further expand their capabilities, including:
- Natural type inference
- Parameter spans
- Improved dictionary support

## Breaking Changes: A Balanced Approach

While backward compatibility is crucial, Torgersen acknowledged that sometimes small, strategic breaking changes are necessary for long-term language health. The team is carefully considering a more permissive approach to breaking changes, focusing on situations where:
- User benefits are clear
- Breaks are localized and diagnosable
- Default fixes are available

## Extensions: Powerful Type Enhancement

Torgersen delved into the concept of 'extensions,' providing a powerful mechanism for extending existing types without modifying their original code. This includes:
- Explicit extensions
- Implicit extensions
- New ways to enhance code readability and organization

## Union Types: A Unified Approach

Union Types are a hot topic in the C# world, sparking discussions about their potential benefits and challenges. Torgersen highlighted the potential for a unified approach, where 'cases' within a union are treated as types themselves, bridging the gap between discriminated and type unions.

## Looking Forward

The future of C# looks incredibly promising, with a focus on developer joy and productivity. The language team's commitment to thoughtful evolution while maintaining C#'s core strengths ensures that it will continue to be a leading choice for developers across all domains.
