Refactor all selected components in @ using following rules @

## Code Standards
- ⚛️ Follow React + TS best practices
- 🧩 Maintain component modularity - Break into smaller focused components
- 📚 Write clean, documented code
- ⚠️ Implement proper error handling
- 🔄 Use proper Git commit messages

### Always follow these rules
- Always build the project after completing any task.
- Use the command: npm run build.
- Iterate until the build is successful.
- Push changes using git push if the build is successful.

> **Note:** Never commit code that doesn't build successfully.