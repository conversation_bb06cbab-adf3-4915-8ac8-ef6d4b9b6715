.face {
  position: absolute;
  width: var(--cube-size);
  height: var(--cube-size);
  /* White background with high contrast */
  background-color: #ffffff;
  border: 2px solid #bbbbbb;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  /* Enhanced box shadow for better visibility */
  box-shadow: 0 0 15px rgba(255, 255, 255, 0.3);
  /* Ensure faces are visible */
  backface-visibility: visible;
  opacity: 1;
}

/* Position faces with responsive transforms */
.front { 
  transform: rotateY(0deg) translateZ(calc(var(--cube-size) * 0.5));
}

.back { 
  transform: rotateY(180deg) translateZ(calc(var(--cube-size) * 0.5));
}

.right { 
  transform: rotateY(90deg) translateZ(calc(var(--cube-size) * 0.5));
}

.left { 
  transform: rotateY(-90deg) translateZ(calc(var(--cube-size) * 0.5));
}

.top { 
  transform: rotateX(90deg) translateZ(calc(var(--cube-size) * 0.5));
}

.bottom { 
  transform: rotateX(-90deg) translateZ(calc(var(--cube-size) * 0.5));
}

/* Simple highlight effect */
.face::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(
    45deg,
    rgba(255, 255, 255, 0.5) 0%,
    rgba(255, 255, 255, 0.8) 100%
  );
  pointer-events: none;
  z-index: 1;
}

.face:hover {
  background-color: #ffffff;
  box-shadow: 0 0 20px rgba(255, 255, 255, 0.4);
}
